{"id": "55745c62-2fac-4db6-b694-821a48ca2f56", "rows_written": 223, "rows_read": 541, "storage_bytes_used": 94208, "write_requests_delegated": 0, "current_frame_no": 172, "top_query_threshold": 4, "top_queries": [{"rows_written": 3, "rows_read": 1, "query": "CREATE TABLE uploaded_medias (bot_username TEXT NOT NULL, media_name TEXT NOT NULL, media_file_id TEXT NOT NULL, PRIMARY KEY (bot_username, media_name));"}, {"rows_written": 3, "rows_read": 1, "query": "CREATE TABLE users (id BIGINT PRIMARY KEY, username TEXT, path TEXT, cart TEXT, balance REAL, pending_invoice_id BIGINT, is_admin BOOLEAN, active_promocode_id TEXT REFERENCES promocodes (id), referred_by BIGINT);"}, {"rows_written": 4, "rows_read": 2, "query": "CREATE TABLE link_product_data (id INTEGER PRIMARY KEY AUTOINCREMENT, product_id TEXT NOT NULL, sold BOOLEAN NOT NULL DEFAULT FALSE, model_name TEXT NOT NULL, medias TEXT NOT NULL, description TEXT NOT NULL, FOREIGN KEY (product_id) REFERENCES products (id));"}, {"rows_written": 4, "rows_read": 4, "query": "DELETE FROM associated_messages WHERE primary_message_id = ? AND user_id = ?;"}, {"rows_written": 8, "rows_read": 0, "query": "INSERT INTO catalog (id, kind, media, description, nav) VALUES ('root', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"🗂 Канал-каталог анкет\",\n            \"url\": \"https://example.com\"\n        },\n        {\n            \"btn\": \"🔐 Пак кружков (обезличенные)\",\n            \"product_category\": \"pack\"\n        },\n        {\n            \"btn\": \"🧚🏻‍♀️ Модели классика\",\n            \"dir\": \"classic\"\n        },\n        {\n            \"btn\": \"🔞 Модели onlyfans\",\n            \"dir\": \"onlyfans\"\n        },\n        {\n            \"btn\": \"🧖🏻‍♀️ Модели домашние\",\n            \"dir\": \"home\"\n        }\n    ]'), ('classic', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"classic-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"classic-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"classic-big\"\n        }\n    ]'), ('onlyfans', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"onlyfans-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"onlyfans-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"onlyfans-big\"\n        }\n    ]'), ('home', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"home-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"home-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"home-big\"\n        }\n    ]');"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT id, media, description FROM catalog WHERE kind = 'category' AND nav IS NULL;"}, {"rows_written": 20, "rows_read": 0, "query": "INSERT INTO catalog (id, kind, media, description, nav) VALUES ('pack', 'category', 'accounts', NULL, NULL), ('classic-small', 'category', 'accounts', NULL, NULL), ('classic-medium', 'category', 'accounts', NULL, NULL), ('classic-big', 'category', 'accounts', NULL, NULL), ('onlyfans-small', 'category', 'accounts', NULL, NULL), ('onlyfans-medium', 'category', 'accounts', NULL, NULL), ('onlyfans-big', 'category', 'accounts', NULL, NULL), ('home-small', 'category', 'accounts', NULL, NULL), ('home-medium', 'category', 'accounts', NULL, NULL), ('home-big', 'category', 'accounts', NULL, NULL);"}, {"rows_written": 0, "rows_read": 22, "query": "SELECT name FROM sqlite_master WHERE type = 'view';"}, {"rows_written": 0, "rows_read": 35, "query": "SELECT name FROM sqlite_master WHERE type = 'table' ORDER BY name;"}], "slowest_query_threshold": 2, "slowest_queries": [{"elapsed_ms": 2, "query": "PRAGMA table_xinfo ('users');", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "REPLACE INTO uploaded_medias (bot_username, media_name, media_file_id) VALUES (?, ?, ?);", "rows_written": 2, "rows_read": 0}, {"elapsed_ms": 2, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;", "rows_written": 0, "rows_read": 14}, {"elapsed_ms": 3, "query": "INSERT INTO catalog (id, kind, media, description, nav) VALUES ('root', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"🗂 Канал-каталог анкет\",\n            \"url\": \"https://example.com\"\n        },\n        {\n            \"btn\": \"🔐 Пак кружков (обезличенные)\",\n            \"product_category\": \"pack\"\n        },\n        {\n            \"btn\": \"🧚🏻‍♀️ Модели классика\",\n            \"dir\": \"classic\"\n        },\n        {\n            \"btn\": \"🔞 Модели onlyfans\",\n            \"dir\": \"onlyfans\"\n        },\n        {\n            \"btn\": \"🧖🏻‍♀️ Модели домашние\",\n            \"dir\": \"home\"\n        }\n    ]'), ('classic', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"classic-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"classic-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"classic-big\"\n        }\n    ]'), ('onlyfans', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"onlyfans-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"onlyfans-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"onlyfans-big\"\n        }\n    ]'), ('home', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"home-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"home-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"home-big\"\n        }\n    ]');", "rows_written": 8, "rows_read": 0}, {"elapsed_ms": 3, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;", "rows_written": 0, "rows_read": 14}, {"elapsed_ms": 3, "query": "SELECT * FROM products WHERE id = ? AND enabled;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 6, "query": "SELECT name FROM sqlite_master WHERE type = 'table' ORDER BY name;", "rows_written": 0, "rows_read": 35}, {"elapsed_ms": 7, "query": "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)", "rows_written": 3, "rows_read": 1}, {"elapsed_ms": 11, "query": "DELETE FROM associated_messages WHERE primary_message_id = ? AND user_id = ?;", "rows_written": 4, "rows_read": 4}, {"elapsed_ms": 11, "query": "SELECT media_file_id FROM uploaded_medias WHERE bot_username = ? AND media_name = ?;", "rows_written": 0, "rows_read": 1}], "embedded_replica_frames_replicated": 0, "query_count": 339, "query_latency": 323430}