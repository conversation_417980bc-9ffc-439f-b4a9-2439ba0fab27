use teloxide::{
    dptree::{case, entry},
    types::{InlineKeyboardMarkup, Message},
};
use tracing::instrument;

use crate::{
    common::{Callback, Step},
    context::Context,
    filter_message,
    media::Media,
    row_btn, row_btn_back, row_btn_cart,
    tiposhop::catalog::CatalogStep,
    Branch, HandlerResult,
};

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
        .map(|ctx: Context| ctx.current_step)
        .branch(case![Step::Search]
            .branch(filter_message()
                .endpoint(on_search_query)
            )
        )
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_search(mut ctx: Context) -> HandlerResult {
    tracing::debug!("on_search");

    let text = "🔍 Введите название модели для поиска:";
    let markup = InlineKeyboardMarkup::new(vec![row_btn_back()]);

    ctx.user.path.push_back(Step::Search);
    ctx.send(Some(text.to_owned()), Media::video("menu"), markup)
        .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_search_query(ctx: Context, message: Message) -> HandlerResult {
    tracing::debug!("on_search_query");

    let Some(query) = message.text() else {
        ctx.send(
            Some("Ожидал от тебя текст, попробуй еще раз".to_owned()),
            Media::video("menu"),
            InlineKeyboardMarkup::new(vec![row_btn_back()]),
        )
        .await?;
        return Ok(());
    };

    let query = query.trim();
    if query.is_empty() {
        ctx.send(
            Some("Поисковый запрос не может быть пустым, попробуй еще раз".to_owned()),
            Media::video("menu"),
            InlineKeyboardMarkup::new(vec![row_btn_back()]),
        )
        .await?;
        return Ok(());
    }

    // Search for products
    let products = ctx.db.search_products(query).await?;

    if products.is_empty() {
        ctx.send(
            Some(format!(
                "🔍 По запросу \"{}\" ничего не найдено.\nПопробуйте другой запрос.",
                query
            )),
            Media::video("menu"),
            InlineKeyboardMarkup::new(vec![
                row_btn("🔍 Новый поиск", Callback::Step(Step::Search)),
                row_btn_cart(),
                row_btn_back(),
            ]),
        )
        .await?;
        return ctx.save_user().await;
    }

    // Create inline keyboard with search results
    let mut buttons = Vec::new();
    for product in &products {
        buttons.push(row_btn(
            &product.name,
            Callback::Step(Step::Catalog(CatalogStep::Product(product.id.clone()))),
        ));
    }

    // Add navigation buttons
    buttons.push(row_btn("🔍 Новый поиск", Callback::Step(Step::Search)));
    buttons.push(row_btn_cart());
    buttons.push(row_btn_back());

    let markup = InlineKeyboardMarkup::new(buttons);

    let text = format!(
        "🔍 Результаты поиска по запросу \"{}\":\n\nНайдено {} моделей:",
        query,
        products.len()
    );

    ctx.send(Some(text), Media::video("menu"), markup).await?;
    ctx.save_user().await
}
