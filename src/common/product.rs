use serde::{de::DeserializeOwned, Deserialize};

use crate::{db::Json, media::Media};

crate::id!(ProductId(String));

crate::id!(ProductInstanceId(i64), Copy);

crate::db_enum!(
    pub enum ProductKind {
        Link,
    }
);

#[derive(Clone, Debug, Deserialize)]
pub struct ProductCategory {
    pub id: String,
    pub media: String,
    pub description: Option<String>,
}

// product kind maps to how product data is stored in db (or else)
// how it is extracted, how it is displayed and everything else
// for links:
#[derive(Clone, Debug, Deserialize)]
pub struct LinkProductData {
    pub id: ProductInstanceId,
    pub product_id: ProductId,
    pub sold: bool,

    pub model_name: String, // also search term
    pub medias: Json<Vec<Media>>,
    pub description: String,
    pub link: String, // link to sell
}

// even though model data is unique and it makes no sense to store it separately from product description,
// i will
// and you won't stop me
// Probably it's best to store them separately, because after we would
// add non-unique products

#[derive(Clone, Debug, Deserialize)]
pub struct ProductDescription {
    pub id: ProductId,
    pub category: Option<String>,
    pub kind: ProductKind,

    pub enabled: bool,

    pub name: String,
    pub price: f64,
    pub production_cost: f64,
}

impl std::hash::Hash for ProductDescription {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.id.hash(state);
    }
}

impl PartialEq for ProductDescription {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for ProductDescription {}
