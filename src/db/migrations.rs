use color_eyre::{eyre::Context as _, Report};
use libsql::{params::IntoParams, Row};

async fn query_one(
    conn: &libsql::Connection,
    sql: &str,
    params: impl IntoParams,
) -> Result<Option<Row>, Report> {
    let mut stmt = conn.prepare(sql).await?;
    let mut rows = stmt.query(params).await?;
    let maybe_row = rows.next().await?;
    Ok(maybe_row)
}

pub async fn migrate(db: &libsql::Connection) -> Result<(), Report> {
    db.execute(
        "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)",
        (),
    )
    .await
    .wrap_err("Failed to create migrations table")?;

    for m in migrations() {
        let current_hash = format!(
            "{:x}",
            md5::compute(m.sql.replace(" ", "").replace("\n", ""))
        );
        let prev_hash_row = query_one(db, "SELECT hash FROM migrations WHERE name =?", &[m.name])
            .await
            .wrap_err("Failed to check migration")?;

        if let Some(prev_hash_row) = prev_hash_row {
            let prev_hash: String = prev_hash_row.get(0)?;

            tracing::debug!("Migration {} hash: {}", m.name, prev_hash);

            if prev_hash == current_hash {
                tracing::debug!("Migration {} was already applied", m.name);
                continue;
            } else {
                tracing::error!("Prev migration found and hash mismatch\nWaiting for `{prev_hash}`, got `{current_hash}`");
                return Err(color_eyre::eyre::eyre!("Migration hash mismatch"));
            }
        }

        let tr = db.transaction().await?;
        match tr.execute_batch(m.sql).await {
            Ok(_) => {
                tr.commit().await?;
            }
            Err(err) => {
                tracing::error!("Failed to apply migration ({}): {}", m.name, err);
                tr.rollback().await?;
                return Err(color_eyre::eyre::eyre!(
                    "Failed to apply migration ({}): {}",
                    m.name,
                    err
                ));
            }
        }

        db.execute(
            "INSERT INTO migrations (name, hash) VALUES (?, ?)",
            &[m.name, &current_hash],
        )
        .await
        .wrap_err("Failed to insert migration")?;
    }

    tracing::info!("Migrations applied");

    Ok(())
}

struct Migration {
    name: &'static str,
    sql: &'static str,
}

fn migrations() -> &'static [Migration] {
    &[Migration {
        name: "init",
        sql: "--sql
                CREATE TABLE catalog (
                    id TEXT PRIMARY KEY,
                    kind TEXT NOT NULL, -- directory, category
                    media TEXT NOT NULL,
                    description TEXT,
                    nav TEXT
                );

                CREATE TABLE products (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    kind TEXT NOT NULL,
                    category TEXT,
                    price REAL,
                    enabled BOOL,
                    production_cost REAL NOT NULL DEFAULT 0
                );

                CREATE TABLE link_product_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id TEXT NOT NULL,
                    sold BOOLEAN NOT NULL DEFAULT FALSE,
                    model_name TEXT NOT NULL,
                    medias TEXT NOT NULL,
                    description TEXT NOT NULL,
                    link TEXT NOT NULL DEFAULT '',
                    FOREIGN KEY (product_id) REFERENCES products(id)
                );

                CREATE TABLE uploaded_medias (
                    bot_username TEXT NOT NULL,
                    media_name TEXT NOT NULL,
                    media_file_id TEXT NOT NULL,

                    PRIMARY KEY (bot_username, media_name)
                );

                CREATE TABLE attributes (
                    key TEXT PRIMARY KEY,
                    value TEXT
                );

                CREATE TABLE promocodes (
                    id PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    text TEXT NOT NULL,
                    kind TEXT NOT NULL DEFAULT 'sale',
                    value INTEGER NOT NULL,
                    start_date TEXT,
                    end_date TEXT,
                    activations INTEGER NOT NULL DEFAULT 0,
                    activations_max INTEGER,
                    hidden BOOLEAN NOT NULL DEFAULT FALSE
                );

                CREATE TABLE promocode_activations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id BIGINT NOT NULL,
                    promo_id TEXT NOT NULL,
                    date TEXT NOT NULL,

                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (promo_id) REFERENCES promocodes(id)
                );

                CREATE TABLE users (
                    id BIGINT PRIMARY KEY,
                    username TEXT,
                    path TEXT,
                    cart TEXT,
                    balance REAL,
                    pending_invoice_id BIGINT,
                    is_admin BOOLEAN,
                    active_promocode_id TEXT REFERENCES promocodes(id),

                    referred_by BIGINT
                );

                CREATE TABLE purchases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id BIGINT NOT NULL,
                    product_id TEXT NOT NULL,
                    promo_id TEXT,
                    paid REAL NOT NULL,
                    date TEXT NOT NULL,
                    referred_by BIGINT,
                    referral_percent BIGINT,
                    production_cost REAL NOT NULL DEFAULT 0
                );

                CREATE TABLE notification_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    kind TEXT NOT NULL UNIQUE,
                    channel_id BIGINT NOT NULL
                );

                CREATE TABLE associated_messages (
                    primary_message_id BIGINT NOT NULL,
                    user_id BIGINT NOT NULL,
                    associated_message_id BIGINT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (primary_message_id, user_id, associated_message_id)
                );
            ",
    }]
}
